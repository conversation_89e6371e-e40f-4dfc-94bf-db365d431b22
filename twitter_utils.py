import glob
import json
import os
import random
from enum import Enum
from typing import Dict, Any, Optional


class BirthdateVisibility(Enum):
    # 生日可见性枚举
    PUBLIC = 'public'
    FOLLOWERS = 'followers'
    FOLLOWING = 'following'
    MUTUAL_FOLLOW = 'mutualfollow'
    PRIVATE = 'self'  # 注意：self就是private，Twitter中'Only you'对应'self'


class DateUtils:
    # 日期处理工具类

    @staticmethod
    def get_max_day_for_month(year: Optional[int], month: int) -> int:
        # 获取指定月份的最大天数
        if month in [1, 3, 5, 7, 8, 10, 12]:
            return 31
        elif month in [4, 6, 9, 11]:
            return 30
        elif month == 2:
            if year and DateUtils.is_leap_year(year):
                return 29
            else:
                return 28
        else:
            return 31

    @staticmethod
    def is_leap_year(year: int) -> bool:
        # 判断是否为闰年
        return year % 4 == 0 and (year % 100 != 0 or year % 400 == 0)

    @staticmethod
    def is_invalid_date_combination(year: Optional[int], month: int, day: int) -> bool:
        # 检查年月日组合是否无效
        max_day = DateUtils.get_max_day_for_month(year, month)
        return day > max_day

    @staticmethod
    def adjust_invalid_date(year: Optional[int], month: int, day: int) -> int:
        # 调整无效日期到该月的最大有效日期
        max_day = DateUtils.get_max_day_for_month(year, month)
        return min(day, max_day)


class TwitterDataValidator:
    # Twitter资料数据校验器

    def __init__(self):
        self.current_year = 2025
        self.supported_image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.webp']

    def validate_profile_data(self, profile_data: Dict[str, Any]) -> Dict[str, Any]:
        # 数据校验
        validation_result = {'valid': True, 'errors': [], 'warnings': []}

        if not isinstance(profile_data, dict):
            validation_result['valid'] = False
            validation_result['errors'].append('profile_data必须是字典类型')
            return validation_result

        self._validate_basic_fields(profile_data, validation_result)
        self._validate_birthdate_fields(profile_data, validation_result)
        self._validate_file_paths(profile_data, validation_result)

        if validation_result['errors']:
            validation_result['valid'] = False

        return validation_result

    def _validate_basic_fields(self, profile_data: Dict[str, Any], validation_result: Dict[str, Any]):
        # 校验基本字段
        if 'name' in profile_data:
            name = profile_data['name']
            if not isinstance(name, str):
                validation_result['errors'].append('name必须是字符串类型')
            elif len(name.strip()) == 0:
                validation_result['errors'].append('name不能为空')
            elif len(name) > 50:
                validation_result['errors'].append('name长度不能超过50个字符')

        if 'bio' in profile_data:
            bio = profile_data['bio']
            if not isinstance(bio, str):
                validation_result['errors'].append('bio必须是字符串类型')
            elif len(bio) > 160:
                validation_result['errors'].append('bio长度不能超过160个字符')

        if 'location' in profile_data:
            location = profile_data['location']
            if not isinstance(location, str):
                validation_result['errors'].append('location必须是字符串类型')
            elif len(location) > 30:
                validation_result['errors'].append('location长度不能超过30个字符')

        if 'website' in profile_data:
            website = profile_data['website']
            if not isinstance(website, str):
                validation_result['errors'].append('website必须是字符串类型')
            elif len(website) > 100:
                validation_result['errors'].append('website长度不能超过100个字符')

    def _validate_birthdate_fields(self, profile_data: Dict[str, Any], validation_result: Dict[str, Any]):
        # 校验生日字段
        if 'birthdate' not in profile_data:
            return

        birthdate = profile_data['birthdate']
        if not isinstance(birthdate, dict):
            validation_result['errors'].append('birthdate必须是字典类型')
            return

        if 'month' in birthdate:
            month = birthdate['month']
            if not isinstance(month, int) or not (1 <= month <= 12):
                validation_result['errors'].append('birthdate.month必须是1-12之间的整数')

        if 'day' in birthdate:
            day = birthdate['day']
            if not isinstance(day, int) or not (1 <= day <= 31):
                validation_result['errors'].append('birthdate.day必须是1-31之间的整数')

        if 'year' in birthdate:
            year = birthdate['year']
            min_year = self.current_year - 18  # 确保年龄大于18岁
            if not isinstance(year, int) or not (1905 <= year <= min_year):
                validation_result['errors'].append(f'birthdate.year必须是1905-{min_year}之间的整数（确保年龄大于18岁）')

        if 'month_day_visibility' in birthdate:
            visibility = birthdate['month_day_visibility']
            valid_values = [e.value for e in BirthdateVisibility]
            if visibility not in valid_values:
                validation_result['errors'].append(f'birthdate.month_day_visibility必须是以下值之一: {valid_values}')

        if 'year_visibility' in birthdate:
            visibility = birthdate['year_visibility']
            valid_values = [e.value for e in BirthdateVisibility]
            if visibility not in valid_values:
                validation_result['errors'].append(f'year_visibility必须是以下值之一: {valid_values}')

    def _validate_file_paths(self, profile_data: Dict[str, Any], validation_result: Dict[str, Any]):
        # 校验文件路径
        if 'avatar_path' in profile_data:
            avatar_path = profile_data['avatar_path']
            if not isinstance(avatar_path, str):
                validation_result['errors'].append('avatar_path必须是字符串类型')
            elif not avatar_path.strip():
                validation_result['errors'].append('avatar_path不能为空')
            elif not os.path.exists(avatar_path):
                validation_result['errors'].append(f'avatar_path指定的文件不存在: {avatar_path}')


class ProfileUtils:
    # 资料工具类

    @staticmethod
    def get_random_avatar_path(assets_dir: str = "assets") -> Optional[str]:
        # 从assets目录中随机选择一个头像图片
        if not os.path.exists(assets_dir):
            return None

        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.webp']:
            image_files.extend(glob.glob(os.path.join(assets_dir, ext)))

        avatar_files = [f for f in image_files if 'background' not in f.lower()]
        if not avatar_files:
            return None

        return random.choice(avatar_files)

    @staticmethod
    def generate_random_birthdate() -> Dict[str, Any]:
        # 生成随机生日信息（确保年龄大于18岁）
        current_year = 2025
        max_birth_year = current_year - 18  # 2007年，确保年龄大于18岁
        year = random.randint(1970, max_birth_year)
        month = random.randint(1, 12)

        if month in [1, 3, 5, 7, 8, 10, 12]:
            day = random.randint(1, 31)
        elif month in [4, 6, 9, 11]:
            day = random.randint(1, 30)
        else:
            day = random.randint(1, 28)

        visibility_options = [e.value for e in BirthdateVisibility]

        return {
            'month': month,
            'day': day,
            'year': year,
            'month_day_visibility': random.choice(visibility_options),
            'year_visibility': random.choice(visibility_options)
        }

    @staticmethod
    def load_profile_data(json_file: str = "资料.json") -> Optional[Dict[str, Any]]:
        # 从JSON文件加载资料数据
        if not os.path.exists(json_file):
            return None

        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)

        required_keys = ['names', 'bios', 'locations', 'websites']
        for key in required_keys:
            if key not in data or not isinstance(data[key], list) or len(data[key]) == 0:
                return None

        return data

    @staticmethod
    def generate_random_profile(json_file: str = "资料.json", assets_dir: str = "assets") -> Optional[Dict[str, Any]]:
        # 生成随机的Twitter资料数据
        profile_data_source = ProfileUtils.load_profile_data(json_file)

        # 如果无法加载数据文件，不使用默认值，直接返回None
        if not profile_data_source:
            return None

        # 使用加载的数据生成随机资料
        return {
            'name': random.choice(profile_data_source['names']),
            'bio': random.choice(profile_data_source['bios']),
            'location': random.choice(profile_data_source['locations']),
            'website': random.choice(profile_data_source['websites']),
            'avatar_path': ProfileUtils.get_random_avatar_path(assets_dir),
            'birthdate': ProfileUtils.generate_random_birthdate()
        }

    @staticmethod
    def get_random_background_path(assets_dir: str = "assets") -> Optional[str]:
        # 从assets/background目录中随机选择一个背景图片
        background_dir = os.path.join(assets_dir, "background")
        if not os.path.exists(background_dir):
            return None

        background_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.gif', '*.webp']:
            background_files.extend(glob.glob(os.path.join(background_dir, ext)))

        if not background_files:
            return None

        return random.choice(background_files)
