import os
import time
from typing import Dict, Any

from DrissionPage import Chromium

from twitter_utils import DateUtils, ProfileUtils


def go_to_profile_edit_page(page):
    """直接跳转到资料编辑页面"""
    try:
        print("正在跳转到资料编辑页面")
        page.get('https://x.com/settings/profile')
        return True
    except Exception as e:
        print(f"跳转编辑页面时发生错误: {str(e)}")
        return False


def wait_for_name_input_and_get_username(page, timeout=10):
    """等待name输入框加载完成并获取当前用户名"""
    try:
        print("正在等待name输入框加载")

        # 等待name输入框加载
        name_input = page.ele('xpath://input[@name="displayName"]', timeout=timeout)
        if not name_input:
            print("未找到name输入框")
            return None

        print("name输入框加载完成")

        # 获取当前用户名
        current_username = name_input.value
        if current_username:
            print(f"获取到当前用户名: {current_username}")
            return current_username
        else:
            print("当前用户名为空")
            return ""

    except Exception as e:
        print(f"获取用户名时发生错误: {str(e)}")
        return None

def update_profile(page, profile_data: Dict[str, Any]):
    """更新Twitter资料"""
    try:
        # 跳转到编辑页面
        if not go_to_profile_edit_page(page):
            return {'success': False, 'message': '无法跳转到资料编辑页面'}

        # 等待页面加载并获取当前用户名
        current_username = wait_for_name_input_and_get_username(page)
        if current_username is None:
            return {'success': False, 'message': '无法加载编辑页面或获取用户名'}

        # 更新各项资料
        result = {
            'success': True,
            'message': '资料更新成功',
            'updated_fields': [],
            'current_username': current_username
        }

        # 处理头像上传
        if profile_data.get('avatar_path'):
            if change_avatar(page, profile_data['avatar_path']):
                result['updated_fields'].append('avatar')
                time.sleep(0.5)

        # 处理背景上传
        if profile_data.get('background_path'):
            if change_background(page, profile_data['background_path']):
                result['updated_fields'].append('background')
                time.sleep(0.5)

        # 更新基本信息
        basic_fields = ['name', 'bio', 'location', 'website']
        for field in basic_fields:
            if profile_data.get(field):
                if change_basic_info(page, field, profile_data[field]):
                    result['updated_fields'].append(field)
                    time.sleep(0.5)

        # 处理生日设置
        if profile_data.get('birthdate'):
            if change_birthdate(page, profile_data['birthdate']):
                result['updated_fields'].append('birthdate')
                time.sleep(0.5)

        # 保存更改
        if not save_changes(page):
            result['success'] = False
            result['message'] = '保存失败'

        return result

    except Exception as e:
        return {'success': False, 'message': f'更新过程中发生异常: {str(e)}'}

def change_avatar(page, avatar_path: str) -> bool:
    """修改头像"""
    try:
        file_inputs = page.eles('@@data-testid=fileInput', timeout=3)
        if not file_inputs or len(file_inputs) < 2:
            return False

        # 使用第2个fileInput进行头像上传
        file_inputs[1].input(avatar_path)

        apply_button = page.ele(
            'xpath://div[@role="dialog"][@aria-modal="true"]//button[@data-testid="applyButton"]', timeout=5)
        if apply_button:
            apply_button.click()
            return True
        return False
    except Exception:
        return False


def change_background(page, background_path: str) -> bool:
    """修改背景"""
    try:
        file_inputs = page.eles('@@data-testid=fileInput', timeout=3)
        if not file_inputs:
            return False

        file_inputs[0].input(background_path)
        apply_button = page.ele(
            'xpath://div[@role="dialog"][@aria-modal="true"]//button[@data-testid="applyButton"]', timeout=5)
        if apply_button:
            apply_button.click()
            return True
        return False
    except Exception:
        return False


def change_basic_info(page, field_name: str, value: str) -> bool:
    """修改基本信息"""
    try:
        field_mapping = {
            'name': 'xpath://input[@name="displayName"]',
            'bio': 'xpath://textarea[@name="description"]',
            'location': 'xpath://input[@name="location"]',
            'website': 'xpath://input[@name="url"]'
        }

        selector = field_mapping.get(field_name)
        if not selector:
            return False

        input_element = page.ele(selector, timeout=3)
        if not input_element:
            return False

        time.sleep(0.5)
        input_element.clear()
        input_element.input(value)
        return True
    except Exception:
        return False

def change_birthdate(page, birthdate_data: Dict[str, Any]) -> bool:
    """修改生日"""
    try:
        # 点击生日按钮
        birthday_button = page.ele('@@data-testid=pivot', timeout=5)
        if not birthday_button:
            return False

        birthday_button.click()

        # 确认编辑生日
        edit_button = page.ele('@@data-testid=confirmationSheetConfirm', timeout=5)
        if edit_button:
            edit_button.click()

        # 等待生日编辑界面
        month_selector = page.ele('@@data-testid=ProfileBirthdate_Month_Selector', timeout=5)
        if not month_selector:
            return False

        # 设置生日值
        selectors = {
            'month': '@@data-testid=ProfileBirthdate_Month_Selector',
            'day': '@@data-testid=ProfileBirthdate_Day_Selector',
            'year': '@@data-testid=ProfileBirthdate_Year_Selector'
        }

        for field, selector in selectors.items():
            if birthdate_data.get(field):
                element = page.ele(selector)
                if element:
                    value = birthdate_data[field]
                    try:
                        if field == 'month':
                            month_names = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                                           'July', 'August', 'September', 'October', 'November', 'December']
                            month_name = month_names[value]
                            element.select.by_text(month_name)
                        elif field == 'day':
                            # 简单日期调整
                            month = birthdate_data.get('month')
                            year = birthdate_data.get('year')
                            if month and DateUtils.is_invalid_date_combination(year, month, value):
                                value = DateUtils.adjust_invalid_date(year, month, value)
                            element.select.by_value(str(value))
                        else:
                            element.select.by_value(str(value))
                    except:
                        pass

        return True
    except Exception:
        return False


def save_changes(page) -> bool:
    """保存更改"""
    try:
        save_button = page.ele('xpath://span[text()="Save"]', timeout=5)
        if not save_button:
            save_button = page.ele('xpath://button[contains(text(), "Save")]', timeout=3)

        if not save_button:
            return False

        # 启动网络监听
        page.listen.start("update_profile")
        save_button.click()

        # 检查生日确认对话框
        confirm_dialog = page.ele('@@data-testid=confirmationSheetDialog', timeout=3)
        if confirm_dialog:
            confirm_btn = page.ele('@@data-testid=confirmationSheetConfirm', timeout=2)
            if confirm_btn:
                confirm_btn.click()

        # 等待网络响应
        try:
            data_packet = page.listen.wait(timeout=300)
            response = data_packet.response

            # 始终打印响应体
            resp_json = response.body
            print(f"网络响应: {resp_json}")

            # 检查HTTP状态码
            if response.status != 200:
                print(f"保存失败: HTTP状态码 {response.status}")
                return False

            # 检查响应体错误
            if isinstance(resp_json, dict) and 'errors' in resp_json:
                print(f"保存失败: 响应包含错误 {resp_json.get('errors')}")
                return False

            return True
        except Exception as e:
            print(f"网络监听异常: {str(e)}")
            return True  # 如果网络监听失败，假设保存成功

    except Exception:
        return False


if __name__ == '__main__':
    def setup_cookies(page, cookies: Dict[str, str]):
        """设置登录cookies"""
        try:
            print("正在设置cookies")
            page.get('https://x.com')
            time.sleep(2)

            page.set.cookies(cookies)
            page.refresh()
            return True
        except Exception as e:
            if "Browser context management is not supported" in str(e):
                return True
            else:
                print(f"设置cookies时发生错误: {str(e)}")
                return False



    # 1. 先生成资料
    print("1. 生成随机资料")
    profile_data = ProfileUtils.generate_random_profile()

    if not profile_data:
        print("无法加载资料数据文件，程序退出")
        exit(1)

    # 添加头像和背景图片路径
    assets_dir = "assets"
    avatar_path = None
    background_path = None

    if os.path.exists(assets_dir):
        try:
            avatar_path = ProfileUtils.get_random_avatar_path(assets_dir)
            background_path = ProfileUtils.get_random_background_path(assets_dir)
            print(f"图片资源准备完成")
            print(f"头像路径: {avatar_path}")
            print(f"背景路径: {background_path}")
        except Exception as e:
            print(f"选择图片时发生错误: {e}")

    print(f"生成的资料数据:")
    for key, value in profile_data.items():
        print(f"  {key}: {value}")

    try:
        # 2. 接管浏览器
        print("\n2. 接管浏览器")
        browser = Chromium(2222)
        tabs = browser.get_tabs()
        target_page = None

        # 查找目标标签页
        for tab in tabs:
            if 'httpbin.org' in tab.url:
                target_page = tab
                break

        if not target_page:
            print("未找到指定的标签页")
            exit(1)

        print("成功连接到浏览器标签页")

        # 设置登录cookies
        cookies = {
            'auth_token': 'f53cfc5468e5e417bdb24b97d039787519e1a479',
            'ct0': '5987d4de06717bbc72566beaab11d88e55a826dfab9364ae1b1182adc2c53680ae520e6489b9fbef5fd70f28e7bc1b3c772e8daf3bde460f747e2ef09621326656abc32ca7c6c0d3c1b386ebf5951c01'
        }

        if not setup_cookies(target_page, cookies):
            print("cookies设置失败，程序退出")
            exit(1)

        print("cookies设置成功")

        # 3. 实例化TwitterDP
        print("\n3. 实例化TwitterDP")
        twitter_dp = TwitterDP()
        print("TwitterDP实例创建成功")

        # 4. 单独测试每个字段
        print("\n4. 开始单独测试各个字段")

        # 测试姓名
        if profile_data.get('name'):
            print(f"\n测试姓名更新: {profile_data['name']}")
            result = twitter_dp.update_profile(target_page, {'name': profile_data['name']})
            print(f"姓名更新结果: {result}")
            time.sleep(2)

        # 测试签名
        if profile_data.get('bio'):
            print(f"\n测试签名更新: {profile_data['bio']}")
            result = twitter_dp.update_profile(target_page, {'bio': profile_data['bio']})
            print(f"签名更新结果: {result}")
            time.sleep(2)

        # 测试地点
        if profile_data.get('location'):
            print(f"\n测试地点更新: {profile_data['location']}")
            result = twitter_dp.update_profile(target_page, {'location': profile_data['location']})
            print(f"地点更新结果: {result}")
            time.sleep(2)

        # 测试网站
        if profile_data.get('website'):
            print(f"\n测试网站更新: {profile_data['website']}")
            result = twitter_dp.update_profile(target_page, {'website': profile_data['website']})
            print(f"网站更新结果: {result}")
            time.sleep(2)

        # 测试头像
        if avatar_path:
            print(f"\n测试头像更新: {avatar_path}")
            result = twitter_dp.update_profile(target_page, {'avatar_path': avatar_path})
            print(f"头像更新结果: {result}")
            time.sleep(2)

        # 测试背景
        if background_path:
            print(f"\n测试背景更新: {background_path}")
            result = twitter_dp.update_profile(target_page, {'background_path': background_path})
            print(f"背景更新结果: {result}")
            time.sleep(2)

        # 测试生日
        if profile_data.get('birthdate'):
            print(f"\n测试生日更新: {profile_data['birthdate']}")
            result = twitter_dp.update_profile(target_page, {'birthdate': profile_data['birthdate']})
            print(f"生日更新结果: {result}")
            time.sleep(2)

        print(f"\n{'='*50}")

    except Exception as e:
        print(f"❌ 程序执行时发生异常: {str(e)}")
        import traceback
        traceback.print_exc()
