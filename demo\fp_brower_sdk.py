# 指纹浏览器目录下
# cmd启动指纹浏览器服务（端口自定义）
# 指纹浏览器API接口.exe --remote-debugging-port=2222

import requests


class FingerPrintBrowser:
    def __init__(self):
        self.fp_base_url = "http://localhost:9001/api/browser"
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        self.proxy_address = ""
        self.proxy_type = ""
        self.proxy_username = ""
        self.proxy_password = ""

        # self.proxy_address = "p.webshare.io:80"
        # self.proxy_type = "socks5"
        # self.proxy_username = "aabczpkn-rotate"
        # self.proxy_password = "tzy0nn7slkhy"

    def browser_create(self, browser_name="user1", base_url="https://www.tiktok.com"):
        """创建浏览器"""
        url = self.fp_base_url + "/create"
        data = {
            "title": browser_name,  # 窗口标题
            "url": 'https://httpbin.org/ip',  # 初始化访问地址
            # "url": '1',  # 初始化访问地址
            # "fingerprint": "",
            "fingerprintType": 0,
            "proxyAddress": self.proxy_address,
            "proxyType": self.proxy_type,
            "proxyUsername": self.proxy_username,
            "proxyPassword": self.proxy_password,
            # "debugPort": 7799,    # 无意义（开发问题）
            "x": 200,  # 屏幕位置 x
            "y": 200,  # 屏幕位置 y
            "width": 400,  # 窗口大小
            "height": 400,  # 窗口大小
            "cachePath": browser_name  # 缓存文件：# D:\brower_zw（指纹浏览器路径）\Cache\user_1
        }
        rp = requests.post(url, headers=self.headers, json=data)
        rp_json = rp.json()
        # {'success': True, 'browserId': '2', 'message': '浏览器创建成功', 'cachePath': 'string'}
        print(f"创建浏览器    ->    {rp_json}")

    def browser_list(self):
        url = self.fp_base_url + "/list"
        rp = requests.get(url, headers=self.headers)
        rp_json = rp.json()
        print(f"浏览器列表    ->    {rp_json}")


if __name__ == '__main__':
    mm = FingerPrintBrowser()
    import time

    mm.browser_create(str(time.time()) + "user")
    # mm.browser_list()
